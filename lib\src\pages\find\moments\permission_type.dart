import 'package:flutter/material.dart';
import 'package:tencent_chat_i18n_tool/tencent_chat_i18n_tool.dart';
import 'package:tencent_cloud_chat_demo/utils/toast.dart';
import './chooseMembers/choose_can.dart';

class PermissionTypePage extends StatefulWidget {
  final String? initialPermissionType;
  final List<String>? initialVisibleFriendList;
  final List<Map<String, String>>? initialVisibleFriendInfoList;
  final List<String>? initialInvisibleFriendList;
  final List<Map<String, String>>? initialInvisibleFriendInfoList;

  const PermissionTypePage({
    super.key,
    this.initialPermissionType,
    this.initialVisibleFriendList,
    this.initialVisibleFriendInfoList,
    this.initialInvisibleFriendList,
    this.initialInvisibleFriendInfoList,
  });

  @override
  State<StatefulWidget> createState() => _PermissionTypePageState();
}

class _PermissionTypePageState extends State<PermissionTypePage> {
  // 查看类型 public,private,custom_visible,custom_invisible
  String permissionType = '';
  final List<Map<String, dynamic>> permissionTypeList = [
    {"name": TIM_t("公开"), "value": "public", "check": true, "isPull": false},
    {"name": TIM_t("私密"), "value": "private", "check": false, "isPull": false},
    {
      "name": TIM_t("部分可见"),
      "value": "custom_visible",
      "check": false,
      "isPull": true
    },
    {
      "name": TIM_t("部分不可见"),
      "value": "custom_invisible",
      "check": false,
      "isPull": true
    }
  ];

  // 添加展开状态变量
  bool _isCustomVisibleExpanded = false;
  bool _isCustomInvisibleExpanded = false;
  List<String> visibleFriendList = [];
  List<String> invisibleFriendList = [];
  List<Map<String, String>> visibleFriendInfoList = []; // 存储ID和昵称
  List<Map<String, String>> invisibleFriendInfoList = []; // 存储ID和昵称

  void _changePermissionType(String value) {
    setState(() {
      permissionType = value;
    });
  }

  // 添加切换展开状态的方法
  void _toggleExpand(String type) {
    setState(() {
      if (type == 'custom_visible') {
        _isCustomVisibleExpanded = !_isCustomVisibleExpanded;
      } else if (type == 'custom_invisible') {
        _isCustomInvisibleExpanded = !_isCustomInvisibleExpanded;
      }
    });
  }

  // 跳转选择可见的朋友
  void _navigateToChooseVisibleFriends() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ChooseCan(
          convType: GroupTypeForUIKit.single,
          preSelectedUserIDs: visibleFriendList, // 传递已选择的好友ID列表
        ),
      ),
    ).then((value) {
      if (value != null) {
        var selectedFriendList = value as List;

        if (selectedFriendList.isNotEmpty) {
          setState(() {
            // 清空之前的列表
            visibleFriendInfoList = [];

            // 使用安全的方式提取userID和昵称
            visibleFriendList = selectedFriendList.map<String>((dynamic e) {
              // 提取ID
              final userID = e.userID?.toString() ?? '';

              // 提取昵称，V2TimFriendInfo类中使用的是userProfile.nickName或friendRemark
              String nickname = userID; // 默认使用userID

              try {
                // 尝试获取friendRemark
                if (e.friendRemark != null &&
                    e.friendRemark.toString().isNotEmpty) {
                  nickname = e.friendRemark.toString();
                }
                // 尝试获取userProfile.nickName
                else if (e.userProfile != null &&
                    e.userProfile.nickName != null &&
                    e.userProfile.nickName.toString().isNotEmpty) {
                  nickname = e.userProfile.nickName.toString();
                }
              } catch (error) {
                debugPrint('获取昵称出错: $error');
              }

              debugPrint('好友ID: $userID, 昵称: $nickname');

              // 将ID和昵称添加到列表中
              visibleFriendInfoList
                  .add({'userID': userID, 'nickname': nickname});

              return userID;
            }).toList();
          });
        }
      }
    });
  }

  // 跳转不给谁看
  void _navigateToChooseInvisibleFriends() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ChooseCan(
          convType: GroupTypeForUIKit.single,
          preSelectedUserIDs: invisibleFriendList, // 传递已选择的好友ID列表
        ),
      ),
    ).then((value) {
      if (value != null) {
        var selectedFriendList = value as List;

        if (selectedFriendList.isNotEmpty) {
          setState(() {
            // 清空之前的列表
            invisibleFriendInfoList = [];

            // 使用安全的方式提取userID和昵称
            invisibleFriendList = selectedFriendList.map<String>((dynamic e) {
              // 提取ID
              final userID = e.userID?.toString() ?? '';

              // 提取昵称，V2TimFriendInfo类中使用的是userProfile.nickName或friendRemark
              String nickname = userID; // 默认使用userID

              try {
                // 尝试获取friendRemark
                if (e.friendRemark != null &&
                    e.friendRemark.toString().isNotEmpty) {
                  nickname = e.friendRemark.toString();
                }
                // 尝试获取userProfile.nickName
                else if (e.userProfile != null &&
                    e.userProfile.nickName != null &&
                    e.userProfile.nickName.toString().isNotEmpty) {
                  nickname = e.userProfile.nickName.toString();
                }
              } catch (error) {
                debugPrint('获取昵称出错: $error');
              }

              debugPrint('好友ID: $userID, 昵称: $nickname');

              // 将ID和昵称添加到列表中
              invisibleFriendInfoList
                  .add({'userID': userID, 'nickname': nickname});

              return userID;
            }).toList();
          });
        }
      }
    });
  }

  @override
  void initState() {
    super.initState();
    permissionType = widget.initialPermissionType ?? 'public';
    visibleFriendList = widget.initialVisibleFriendList ?? [];
    visibleFriendInfoList = widget.initialVisibleFriendInfoList ?? [];
    invisibleFriendList = widget.initialInvisibleFriendList ?? [];
    invisibleFriendInfoList = widget.initialInvisibleFriendInfoList ?? [];
    debugPrint("initialVisibleFriendList: ${widget.initialVisibleFriendList}");
    debugPrint(
        "initialVisibleFriendInfoList: ${widget.initialVisibleFriendInfoList}");
    debugPrint(
        "initialInvisibleFriendList: ${widget.initialInvisibleFriendList}");
    debugPrint(
        "initialInvisibleFriendInfoList: ${widget.initialInvisibleFriendInfoList}");
    // 根据初始权限类型设置展开状态
    if (permissionType == 'custom_visible') {
      _isCustomVisibleExpanded = true;
    } else if (permissionType == 'custom_invisible') {
      _isCustomInvisibleExpanded = true;
    }

    // 更新选中状态
    for (var item in permissionTypeList) {
      item['check'] = item['value'] == permissionType;
    }
  }

  void handlePermissionType() {
    if (permissionType == 'custom_visible') {
      if (visibleFriendList.isEmpty) {
        ToastUtils.toast(TIM_t("请至少选择一个可见的朋友"));
        return;
      }
      Navigator.pop(context, {
        "visibleFriendList": visibleFriendList,
        "permissionType": permissionType,
        "visibleFriendInfoList": visibleFriendInfoList
      });
      return;
    }
    if (permissionType == 'custom_invisible') {
      if (invisibleFriendList.isEmpty) {
        ToastUtils.toast(TIM_t("请至少选择一个不可见的朋友"));
        return;
      }
      Navigator.pop(context, {
        "invisibleFriendList": invisibleFriendList,
        "permissionType": permissionType,
        "invisibleFriendInfoList": invisibleFriendInfoList
      });
      return;
    }

    Navigator.pop(context, {"permissionType": permissionType});
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        backgroundColor: Colors.white,
        appBar: AppBar(
          backgroundColor: Colors.white,

          automaticallyImplyLeading: false,
          // 禁用自动生成的返回按钮
          leading: null,
          // 移除leading
          leadingWidth: 0,
          // 设置leading宽度为0
          actions: [
            // 在actions中添加自定义宽度的取消按钮
            Padding(
              padding: const EdgeInsets.only(left: 16),
              child: GestureDetector(
                onTap: () {
                  Navigator.pop(context);
                },
                child: Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
                  margin: const EdgeInsets.only(right: 16),
                  height: 32,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(8),
                    color: const Color(0xFFE9E9E9),
                  ),
                  child: Center(
                    child: Text(TIM_t('取消'),
                        style: const TextStyle(color: Color(0XFF666666))),
                  ),
                ),
              ),
            ),
            const Spacer(), // 添加一个Spacer将发布按钮推到右边

            Text(TIM_t("谁可以看")),

            const Spacer(), // 添加一个Spacer将发布按钮推到右边
            // 添加发布按钮
            Padding(
              padding: const EdgeInsets.only(right: 16),
              child: GestureDetector(
                onTap: () {
                  // 处理选择的权限类型
                  handlePermissionType();
                },
                child: Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
                  height: 32,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(8),
                    color: const Color(0xFF0072FC),
                  ),
                  child: Center(
                    child: Text(TIM_t('确定'),
                        style: const TextStyle(color: Colors.white)),
                  ),
                ),
              ),
            ),
          ],
        ),
        body: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 32),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              InkWell(
                onTap: () {
                  _changePermissionType("public");
                },
                child: Container(
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  decoration: const BoxDecoration(
                    border: Border(
                      bottom: BorderSide(
                        color: Color(0xFFE9E9E9),
                        width: 0,
                      ),
                    ),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      permissionType == "public"
                          ? Image.asset(
                              'assets/moments/check.png',
                              width: 16,
                              height: 16,
                            )
                          : const SizedBox(
                              width: 16,
                            ),
                      const SizedBox(width: 12),
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(TIM_t("公开"),
                              style: const TextStyle(
                                  color: Color(0xFF333333), fontSize: 12)),
                          Text(TIM_t("所有朋友可见"),
                              style: const TextStyle(
                                  color: Color(0xFF666666), fontSize: 10)),
                        ],
                      ),
                      const Spacer(),
                    ],
                  ),
                ),
              ),
              InkWell(
                onTap: () {
                  _changePermissionType("private");
                },
                child: Container(
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  decoration: const BoxDecoration(
                    border: Border(
                      bottom: BorderSide(
                        color: Color(0xFFE9E9E9),
                        width: 0,
                      ),
                    ),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      permissionType == "private"
                          ? Image.asset(
                              'assets/moments/check.png',
                              width: 16,
                              height: 16,
                            )
                          : const SizedBox(
                              width: 16,
                            ),
                      const SizedBox(width: 12),
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(TIM_t("私密"),
                              style: const TextStyle(
                                  color: Color(0xFF333333), fontSize: 12)),
                          Text(TIM_t("仅自己可见"),
                              style: const TextStyle(
                                  color: Color(0xFF666666), fontSize: 10)),
                        ],
                      ),
                      const Spacer(),
                    ],
                  ),
                ),
              ),
              InkWell(
                onTap: () {
                  _changePermissionType("custom_visible");
                  _toggleExpand("custom_visible");
                },
                child: Container(
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  decoration: const BoxDecoration(
                    border: Border(
                      bottom: BorderSide(
                        color: Color(0xFFE9E9E9),
                        width: 0,
                      ),
                    ),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      permissionType == "custom_visible"
                          ? Image.asset(
                              'assets/moments/check.png',
                              width: 16,
                              height: 16,
                            )
                          : const SizedBox(
                              width: 16,
                            ),
                      const SizedBox(width: 12),
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(TIM_t("部分可见"),
                              style: const TextStyle(
                                  color: Color(0xFF333333), fontSize: 12)),
                        ],
                      ),
                      const Spacer(),
                      Image.asset(
                        _isCustomVisibleExpanded
                            ? 'assets/moments/icon_down.png'
                            : 'assets/moments/icon_up.png',
                        width: 16,
                        height: 16,
                      )
                    ],
                  ),
                ),
              ),
              _isCustomVisibleExpanded
                  ? InkWell(
                      onTap: () {
                        _navigateToChooseVisibleFriends();
                      },
                      child: Container(
                        padding: const EdgeInsets.only(top: 16, bottom: 16),
                        margin: const EdgeInsets.only(left: 28),
                        width: double.infinity,
                        decoration: const BoxDecoration(
                          border: Border(
                            bottom: BorderSide(
                              color: Color(0xFFE9E9E9),
                              width: 1.0,
                            ),
                          ),
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(TIM_t("选择朋友"),
                                style: const TextStyle(
                                  color: Color(0xFF295386),
                                  fontSize: 10,
                                  fontWeight: FontWeight.w500,
                                )),
                            visibleFriendInfoList.isNotEmpty
                                ? Text(
                                    visibleFriendInfoList
                                        .map((e) => e['nickname'] ?? '')
                                        .join(", "),
                                    style: const TextStyle(
                                      color: Color(0xFF666666),
                                      fontSize: 10,
                                      fontWeight: FontWeight.w500,
                                    ),
                                  )
                                : Text(
                                    TIM_t("选择可见的朋友"),
                                    style: const TextStyle(
                                      color: Color(0xFF666666),
                                      fontSize: 10,
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                          ],
                        ),
                      ),
                    )
                  : const SizedBox(),
              InkWell(
                onTap: () {
                  _changePermissionType("custom_invisible");
                  _toggleExpand("custom_invisible");
                },
                child: Container(
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  decoration: const BoxDecoration(
                    border: Border(
                      bottom: BorderSide(
                        color: Color(0xFFE9E9E9),
                        width: 0,
                      ),
                    ),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      permissionType == "custom_invisible"
                          ? Image.asset(
                              'assets/moments/check.png',
                              width: 16,
                              height: 16,
                            )
                          : const SizedBox(
                              width: 16,
                            ),
                      const SizedBox(width: 12),
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(TIM_t("不给谁看"),
                              style: const TextStyle(
                                  color: Color(0xFF333333), fontSize: 12)),
                        ],
                      ),
                      const Spacer(),
                      Image.asset(
                        _isCustomInvisibleExpanded
                            ? 'assets/moments/icon_down.png'
                            : 'assets/moments/icon_up.png',
                        width: 16,
                        height: 16,
                      )
                    ],
                  ),
                ),
              ),
              _isCustomInvisibleExpanded
                  ? InkWell(
                      onTap: () {
                        _navigateToChooseInvisibleFriends();
                      },
                      child: Container(
                        padding: const EdgeInsets.only(top: 16, bottom: 16),
                        margin: const EdgeInsets.only(left: 28),
                        width: double.infinity,
                        decoration: const BoxDecoration(
                          border: Border(
                            bottom: BorderSide(
                              color: Color(0xFFE9E9E9),
                              width: 1.0,
                            ),
                          ),
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(TIM_t("选择朋友"),
                                style: const TextStyle(
                                  color: Color(0xFF295386),
                                  fontSize: 10,
                                  fontWeight: FontWeight.w500,
                                )),
                            invisibleFriendInfoList.isNotEmpty
                                ? Text(
                                    invisibleFriendInfoList
                                        .map((e) => e['nickname'] ?? '')
                                        .join(", "),
                                    style: const TextStyle(
                                      color: Color(0xFF666666),
                                      fontSize: 10,
                                      fontWeight: FontWeight.w500,
                                    ),
                                  )
                                : Text(
                                    TIM_t("选择不可见的朋友"),
                                    style: const TextStyle(
                                      color: Color(0xFF666666),
                                      fontSize: 10,
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                          ],
                        ),
                      ),
                    )
                  : const SizedBox()
            ],
          ),
        ));
  }
}
