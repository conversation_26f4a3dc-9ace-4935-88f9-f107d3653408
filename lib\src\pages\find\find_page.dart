import 'package:flutter/material.dart';
import 'package:tencent_cloud_chat_sdk/models/v2_tim_value_callback.dart';
import '../../../utils/toast.dart';
import '../../provider/local_setting.dart';
import 'moments/moments_page.dart';
import 'package:provider/provider.dart';
import 'package:tencent_chat_i18n_tool/tools/i18n_tool.dart';
import 'package:tencent_cloud_chat_sdk/tencent_im_sdk_plugin.dart';

class FindPage extends StatefulWidget {
  const FindPage({super.key});

  @override
  State<StatefulWidget> createState() => _FindPageState();
}

class _FindPageState extends State<FindPage> {
  @override
  void initState() {
    // TODO: implement initState
    super.initState();

  }

  _jumpPage(key) async {
    if (key == "pyq") {
      // 用户信息sdk
      V2TimValueCallback<String> self =
          await TencentImSDKPlugin.v2TIMManager.getLoginUser();
      final data = await TencentImSDKPlugin.v2TIMManager
          .getUsersInfo(userIDList: [self.data!]);
      final selfInfo = data.data?[0];

      Navigator.push(
          context,
          MaterialPageRoute(
              builder: (context) => MomentsPage(self: selfInfo!)));
    }


    if (key == "sph") {
      ToastUtils.toast("视频号开发中");
    }
    if (key == "yx") {
      ToastUtils.toast("趣味游戏开发中");
    }
  }

  @override
  Widget build(BuildContext context) {
    final LocalSetting localSetting = Provider.of<LocalSetting>(context);

    return Container(
      color: const Color(0xFFF9F9F9),
      child: Column(

        children: [

          const SizedBox(
            height: 12,
          ),
          Material(
              color: const Color(0xFFFFFFFF),
              child: InkWell(
                onTap: () {
                  _jumpPage("pyq");
                },
                child: Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 16, vertical: 9),
                  height: 48,
                  child: Row(
                    children: [
                      Image.asset(
                        "assets/find/pyq.png",
                        width: 24,
                        height: 24,
                      ),
                      const SizedBox(
                        width: 10,
                      ),
                      Text(
                        TIM_t("朋友圈"),
                        style: const TextStyle(
                            color: Color(0xFF333333),
                            fontSize: 14,
                            fontWeight: FontWeight.w500),
                      ),
                      const Spacer(),
                      Image.asset(
                        "assets/find/icon_right.png",
                        width: 16,
                        height: 16,
                      ),
                    ],
                  ),
                ),
              )),
          const SizedBox(
            height: 12,
          ),
          Material(
            color: const Color(0xFFFFFFFF),
            child: InkWell(
              onTap: () {
                _jumpPage("sph");
              },
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 9),
                height: 48,
                child: Row(
                  children: [
                    Image.asset(
                      "assets/find/sph.png",
                      width: 24,
                      height: 24,
                    ),
                    const SizedBox(
                      width: 10,
                    ),
                    Text(
                      TIM_t("视频号"),
                      style: const TextStyle(
                          color: Color(0xFF333333),
                          fontSize: 14,
                          fontWeight: FontWeight.w500),
                    ),
                    const Spacer(),
                    Image.asset(
                      "assets/find/icon_right.png",
                      width: 16,
                      height: 16,
                    ),
                  ],
                ),
              ),
            ),
          ),
          const SizedBox(
            height: 12,
          ),
          Material(
            color: const Color(0xFFFFFFFF),
            child: InkWell(
              onTap: () {
                _jumpPage("yx");
              },
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 9),
                height: 48,
                child: Row(
                  children: [
                    Image.asset(
                      "assets/find/yx.png",
                      width: 24,
                      height: 24,
                    ),
                    const SizedBox(
                      width: 10,
                    ),
                    Text(
                      TIM_t("趣味游戏"),
                      style: const TextStyle(
                          color: Color(0xFF333333),
                          fontSize: 14,
                          fontWeight: FontWeight.w500),
                    ),
                    const Spacer(),
                    Image.asset(
                      "assets/find/icon_right.png",
                      width: 16,
                      height: 16,
                    ),
                  ],
                ),
              ),
            ),
          )        ],
      ),
    );
  }

}
