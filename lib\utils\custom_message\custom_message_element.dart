import 'dart:async';
import 'dart:convert';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:tencent_chat_i18n_tool/tencent_chat_i18n_tool.dart';
import 'package:tencent_cloud_chat_demo/utils/custom_message/calling_message/calling_message_data_provider.dart';
import 'package:tencent_cloud_chat_demo/utils/custom_message/calling_message/group_call_message_builder.dart';
import 'package:tencent_cloud_chat_demo/utils/custom_message/calling_message/single_call_message_builder.dart';
import 'package:tencent_cloud_chat_demo/utils/custom_message/red_envelope_message.dart';
import 'package:tencent_cloud_chat_demo/utils/user_info_local.dart';
import 'package:tencent_cloud_chat_sdk/models/v2_tim_friend_info_result.dart';
import 'package:tencent_cloud_chat_sdk/models/v2_tim_message.dart';
import 'package:tencent_cloud_chat_sdk/models/v2_tim_message_change_info.dart';
import 'package:tencent_cloud_chat_sdk/models/v2_tim_user_full_info.dart';
import 'package:tencent_cloud_chat_sdk/models/v2_tim_value_callback.dart';
import 'package:tencent_cloud_chat_sdk/tencent_im_sdk_plugin.dart';
import 'package:tencent_cloud_chat_uikit/tencent_cloud_chat_uikit.dart';
import 'package:tencent_cloud_chat_uikit/theme/tui_theme.dart';
import 'package:tencent_cloud_chat_uikit/ui/controller/tim_uikit_chat_controller.dart';
import 'package:tencent_cloud_chat_uikit/ui/utils/message.dart';
import 'package:tencent_cloud_chat_uikit/ui/widgets/link_preview/common/extensions.dart';
import 'package:tencent_cloud_chat_uikit/ui/widgets/link_preview/common/utils.dart';
import 'package:tencent_cloud_chat_demo/src/provider/theme.dart';
import 'package:flutter_markdown/flutter_markdown.dart';
import 'package:tencent_cloud_chat_demo/utils/custom_message/link_message.dart';
import 'package:tencent_cloud_chat_demo/utils/custom_message/web_link_message.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:cached_network_image/cached_network_image.dart';
import './red_envelope_detail_dialog.dart';
import './packet_receive_message.dart';
import '../../src/pages/red_envelope/red_packet_collection.dart';
import '../toast.dart';
import 'package:tencent_cloud_chat_sdk/models/v2_tim_msg_create_info_result.dart';
import 'package:tencent_cloud_chat_sdk/models/v2_tim_conversation.dart';
import '../../apis/redPacket_api.dart';
import 'isReceivedRedPacket.dart';

class CustomMessageElem extends StatefulWidget {
  final TextStyle? messageFontStyle;
  final BorderRadius? messageBorderRadius;
  final Color? messageBackgroundColor;
  final EdgeInsetsGeometry? textPadding;
  final V2TimMessage message;
  final bool isShowJump;
  final VoidCallback? clearJump;
  final TIMUIKitChatController chatController;
  final V2TimConversation? conversation;
  final String myUserID;

  const CustomMessageElem({
    Key? key,
    required this.message,
    required this.isShowJump,
    required this.chatController,
    this.clearJump,
    this.messageFontStyle,
    this.messageBorderRadius,
    this.messageBackgroundColor,
    this.textPadding,
    this.conversation,
    required this.myUserID,
  }) : super(key: key);

  static Future<void> launchWebURL(BuildContext context, String url) async {
    try {
      await launchUrl(
        Uri.parse(url).withScheme,
        mode: LaunchMode.externalApplication,
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text(TIM_t("无法打开URL"))), // Cannot launch the url
      );
    }
  }

  // 判断自己是否领取过这个红包，查询openusers中是否包含自己的用户id
  bool isReceivedRedPacket(List<dynamic> openusers) {
    // 拿到自己的用户信息
    String? currentUserID = TIMUIKitCore.getInstance().loginUserInfo?.userID;
    return openusers.contains(currentUserID);
  }

  // 处理红包点击事件
  void handleRedEnvelopeClick(BuildContext context, V2TimMessage message,
      RedEnvelopeMessage? messageInfo) async {
    if (conversation?.type == 2) {
      handleGroupRedEnvelopeClick(context, message, messageInfo);
      return;
    }

    bool isSelf = message.isSelf ?? false;
    // if (isSelf) {
    //   return;
    // }
    debugPrint("红包点击事件， 消息详情: ${messageInfo?.toString()}");
    String senderName = '';
    if (messageInfo == null) return;
    if (message.friendRemark?.isNotEmpty ?? false) {
      senderName = message.friendRemark!;
    } else if (message.nickName?.isNotEmpty ?? false) {
      senderName = message.nickName!;
    } else {
      senderName = message.sender!;
    }
    // 普通红包的判断
    if (messageInfo.isOpen == true) {
      // 查询红包消息
      ToastUtils.showLoading();
      Api.instance.getRedPacketDetail(messageInfo.redPacketId!).then((res) {
        debugPrint("红包详情1: ${res.toJson()}");
        if (res.code == 0 && res.ok!) {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => RedPacketCollectionPage(
                senderName: senderName,
                remark: messageInfo.remark ?? '',
                amount: messageInfo.amount ?? '',
                packetDetail: res,
                isOpen: messageInfo.isOpen ?? true,
                message: message,
              ),
            ),
          );
        }
        ToastUtils.hideLoading();
      }).catchError((e) {
        ToastUtils.hideLoading();
        debugPrint("查询红包消息失败: ${e.toString()}");
      });

      return;
    }

    if (messageInfo.isOpen == false || isSelf) {
      // 判断红包是否过期
      ToastUtils.showLoading();
      Api.instance.getRedPacketDetail(messageInfo.redPacketId!).then((res) {
        if (res.code == 0 && res.ok!) {
          if (res.data?.status == 'expired' && isSelf == false) {
            showDialog(
              context: context,
              barrierDismissible: false,
              builder: (context) => RedEnvelopeDetailDialog(
                redEnvelopeMessage: messageInfo,
                isExpired: true,
                messageID: message.id ?? '',
                senderName: senderName, // 或者从消息中获取发送者名称
                onOpen: () {},
              ),
            );
            handleReplaceRedEnvelopeMessage(
                context, message, messageInfo, true);
          } else if (res.data?.status == 'active' && isSelf == false) {
            showDialog(
              context: context,
              barrierDismissible: false,
              builder: (context) => RedEnvelopeDetailDialog(
                redEnvelopeMessage: messageInfo,
                isExpired: false,
                messageID: message.id ?? '',
                senderName: senderName, // 或者从消息中获取发送者名称
                onOpen: () {
                  // ToastUtils.showLoading(message: TIM_t("正在加载..."));
                  // 调用领取红包接口
                  Api.instance
                      .receiveRedPacket(messageInfo.redPacketId)
                      .then((res) {
                    if (res.code == 0 && res.ok!) {
                      if (res.data?.status == 'success') {
                        handleReplaceRedEnvelopeMessage(
                            context, message, messageInfo, false);
                        Api.instance
                            .getRedPacketDetail(messageInfo.redPacketId!)
                            .then((res) {
                          if (res.code == 0 && res.ok!) {
                            Navigator.of(context).pop();
                            Navigator.push(
                              context,
                              MaterialPageRoute(
                                builder: (context) => RedPacketCollectionPage(
                                  senderName: senderName,
                                  remark: messageInfo.remark ?? '',
                                  amount: messageInfo.amount ?? '',
                                  packetDetail: res,
                                  isOpen: true,
                                  message: message,
                                ),
                              ),
                            );
                          }
                        });
                      } else if (res.data?.status == 'empty' &&
                          res.data?.amount == null) {
                        handleReplaceRedEnvelopeMessage(
                            context, message, messageInfo, true);
                        Api.instance
                            .getRedPacketDetail(messageInfo.redPacketId!)
                            .then((res) {
                          if (res.code == 0 && res.ok!) {
                            Navigator.of(context).pop();
                            Navigator.push(
                              context,
                              MaterialPageRoute(
                                builder: (context) => RedPacketCollectionPage(
                                  senderName: senderName,
                                  remark: messageInfo.remark ?? '',
                                  amount: messageInfo.amount ?? '',
                                  packetDetail: res,
                                  isOpen: true,
                                  message: message,
                                ),
                              ),
                            );
                          }
                        });
                      } else {
                        ToastUtils.toast(res.msg!);
                      }
                    }
                    debugPrint("领取红包接口返回: ${res.toJson()}");
                  });
                },
              ),
            );
          } else if (res.data?.status == 'active' && isSelf) {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => RedPacketCollectionPage(
                  senderName: senderName,
                  remark: messageInfo.remark ?? '',
                  amount: messageInfo.amount ?? '',
                  packetDetail: res,
                  isOpen: messageInfo.isOpen ?? true,
                  message: message,
                ),
              ),
            );
          } else if (res.data?.status == 'expired' && isSelf) {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => RedPacketCollectionPage(
                  senderName: senderName,
                  remark: messageInfo.remark ?? '',
                  amount: messageInfo.amount ?? '',
                  packetDetail: res,
                  isOpen: messageInfo.isOpen ?? true,
                  message: message,
                  isExpired: true,
                ),
              ),
            );
          }
        }
        ToastUtils.hideLoading();
      }).catchError((e) {
        ToastUtils.hideLoading();
        debugPrint("查询红包消息失败: ${e.toString()}");
      });

      return;
    }
  }

// 处理群聊红包的点击事件
  Future<void> handleGroupRedEnvelopeClick(BuildContext context,
      V2TimMessage message, RedEnvelopeMessage? messageInfo) async {
    debugPrint("红包点击事件， 消息详情: ${message.toJson()}");
    String senderName = '';
    bool isLucky = messageInfo?.type == 'lucky';
    if (message.friendRemark?.isNotEmpty ?? false) {
      senderName = message.friendRemark!;
    } else if (message.nickName?.isNotEmpty ?? false) {
      senderName = message.nickName!;
    } else {
      senderName = message.sender!;
    }
    bool isReceived = isReceivedRedPacket(messageInfo!.openUsers!);
    bool isSelf = message.isSelf ?? false;

    final userInfo = await UserInfoLocal.getUserInfo();
   
    if (isLucky && isReceived) {
      // 查询红包消息
      ToastUtils.showLoading();
      Api.instance.getRedPacketDetail(messageInfo!.redPacketId!).then((res) {
        if (res.code == 0 && res.ok!) {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => RedPacketCollectionPage(
                senderName: senderName,
                remark: messageInfo.remark ?? '',
                amount: messageInfo.amount ?? '',
                packetDetail: res,
                isOpen: messageInfo.isOpen ?? true,
                userId: userInfo?.userId ?? '',
                message: message,
              ),
            ),
          );
        }
        ToastUtils.hideLoading();
      }).catchError((e) {
        ToastUtils.hideLoading();
        debugPrint("查询红包消息失败: ${e.toString()}");
      });

      return;
    }
    if ((!isLucky && isReceived) || (!isLucky && isSelf)) {
      // 查询红包消息
      ToastUtils.showLoading();
      Api.instance.getRedPacketDetail(messageInfo!.redPacketId!).then((res) {
        if (res.code == 0 && res.ok!) {
          debugPrint("领取红包接口22222: ${res.data?.toJson()}");
          if (res.data?.status == 'expired') {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => RedPacketCollectionPage(
                senderName: senderName,
                remark: messageInfo.remark ?? '',
                amount: messageInfo.amount ?? '',
                packetDetail: res,
                isOpen: messageInfo.isOpen ?? true,
                userId: userInfo?.userId ?? '',
                message: message,
                isExpired: true,
              ),
            ),
          );
          }else {
            Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => RedPacketCollectionPage(
                senderName: senderName,
                remark: messageInfo.remark ?? '',
                amount: messageInfo.amount ?? '',
                packetDetail: res,
                isOpen: messageInfo.isOpen ?? true,
                userId: userInfo?.userId ?? '',
                message: message,
              ),
            ),
          );
          }
        }
        ToastUtils.hideLoading();
      }).catchError((e) {
        ToastUtils.hideLoading();
        debugPrint("查询红包消息失败: ${e.toString()}");
      });
      return;
    }
    // 判断普通群红包是否过期
    if ((!isLucky && !isReceived) || (!isLucky && isSelf)) {
      // 查询红包消息
      ToastUtils.showLoading();
      Api.instance.getRedPacketDetail(messageInfo!.redPacketId!).then((res) {
        if (res.code == 0 && res.ok!) {
          if (res.data?.status == 'expired') {
            showDialog(
              context: context,
              barrierDismissible: false,
              builder: (context) => RedEnvelopeDetailDialog(
                redEnvelopeMessage: messageInfo,
                isExpired: true,
                messageID: message.id ?? '',
                senderName: senderName, // 或者从消息中获取发送者名称
                onOpen: () async {},
              ),
            );
            handleReplaceRedEnvelopeMessage(
                context, message, messageInfo, true);
          }else {
            showDialog(
              context: context,
              barrierDismissible: false,
              builder: (context) => RedEnvelopeDetailDialog(
                redEnvelopeMessage: messageInfo,
                isExpired: false,
                messageID: message.id ?? '',
                senderName: senderName, // 或者从消息中获取发送者名称
                onOpen: () async {
                  // ToastUtils.showLoading(message: TIM_t("正在加载..."));
                  // 调用领取红包接口
                  Api.instance
                      .receiveRedPacket(messageInfo.redPacketId)
                      .then((res) {
                    if (res.code == 0 && res.ok!) {
                      if (res.data?.status == 'success') {
                        handleReplaceRedEnvelopeMessage(
                            context, message, messageInfo, false);
                        Api.instance
                            .getRedPacketDetail(messageInfo.redPacketId!)
                            .then((res) {
                          if (res.code == 0 && res.ok!) {
                            Navigator.of(context).pop();
                            Navigator.push(
                              context,
                              MaterialPageRoute(
                                builder: (context) => RedPacketCollectionPage(
                                    senderName: senderName,
                                    remark: messageInfo.remark ?? '',
                                    amount: messageInfo.amount ?? '',
                                    packetDetail: res,
                                    isOpen: true,
                                    userId: userInfo?.userId ?? '',
                                    message: message),
                              ),
                            );
                          }
                        });
                      }
                    }
                    debugPrint("领取红包接口返回: ${res.toJson()}");
                  });
                },
              ),
            );
          }
        }
        ToastUtils.hideLoading();
      }).catchError((e) {
        ToastUtils.hideLoading();
        debugPrint("查询红包消息失败: ${e.toString()}");
      });
      return;
    }

    // 判断拼手气群红包是否过期
    if (isLucky && !isReceived) {
      ToastUtils.showLoading();
      Api.instance.getRedPacketDetail(messageInfo!.redPacketId!).then((res) {
        if (res.code == 0 && res.ok!) {
          if (res.data?.status == 'expired') {
            showDialog(
              context: context,
              barrierDismissible: false,
              builder: (context) => RedEnvelopeDetailDialog(
                redEnvelopeMessage: messageInfo,
                isExpired: true,
                messageID: message.id ?? '',
                senderName: senderName, // 或者从消息中获取发送者名称
                onOpen: () async {},
              ),
            );
          handleReplaceRedEnvelopeMessage(
                context, message, messageInfo, true);
          } else {
            // 如果是拼手气红包，自己也能领自己的
            showDialog(
              context: context,
              barrierDismissible: false,
              builder: (context) => RedEnvelopeDetailDialog(
                redEnvelopeMessage: messageInfo,
                isExpired: false,
                messageID: message.id ?? '',
                senderName: senderName, // 或者从消息中获取发送者名称
                onOpen: () async {
                  // ToastUtils.showLoading(message: TIM_t("正在加载..."));
                  // 调用领取红包接口
                  Api.instance
                      .receiveRedPacket(messageInfo.redPacketId)
                      .then((res) {
                    if (res.code == 0 && res.ok!) {
                      if (res.data?.status == 'success') {
                        handleReplaceRedEnvelopeMessage(
                            context, message, messageInfo, false);
                        Api.instance
                            .getRedPacketDetail(messageInfo.redPacketId!)
                            .then((res) {
                          if (res.code == 0 && res.ok!) {
                            Navigator.of(context).pop();
                            Navigator.push(
                              context,
                              MaterialPageRoute(
                                builder: (context) => RedPacketCollectionPage(
                                    senderName: senderName,
                                    remark: messageInfo.remark ?? '',
                                    amount: messageInfo.amount ?? '',
                                    packetDetail: res,
                                    isOpen: true,
                                    userId: userInfo?.userId ?? '',
                                    message: message),
                              ),
                            );
                          }
                        });
                      }
                    }
                    debugPrint("领取红包接口返回: ${res.toJson()}");
                  });
                },
              ),
            );
          }
        }
        ToastUtils.hideLoading();
      }).catchError((e) {
        ToastUtils.hideLoading();
        debugPrint("查询红包消息失败: ${e.toString()}");
      });
      return;
    }
  }

// 二次修改消息用于领取红包
  Future<void> handleReplaceRedEnvelopeMessage(
      BuildContext context,
      V2TimMessage message,
      RedEnvelopeMessage? messageInfo,
      bool isEmpty) async {
    // 通过消息id查找到需要修改的消息
    V2TimValueCallback<List<V2TimMessage>> msgListRes = await TencentImSDKPlugin
        .v2TIMManager
        .getMessageManager()
        .findMessages(messageIDList: [message.msgID ?? '']); // 查找需要修改消息的id
    // debugPrint("查询信息: ${msgListRes.toJson()}");
    // 编辑消息
    if (msgListRes.code == 0) {
      List<V2TimMessage>? messageList = msgListRes.data;
      // debugPrint("修改消息: ${messageList?[0].customElem?.data}");
      if (messageList!.isNotEmpty) {
        // 拿到需要修改的信息
        V2TimMessage originMessage = messageList[0];
        // 信息模板 {"redEnvelopeID":"red_envelope","isOpen":false,"image":"...","remark":"...","amount":"...","isDefault":false,"version":4}
        Map<String, dynamic> diyMsg =
            jsonDecode(originMessage.customElem?.data ?? '');
        diyMsg['isOpen'] = true;
        diyMsg['openUsers'] = diyMsg['openUsers'] ?? [];

        // 拿到自己的用户信息
        String? currentUserID =
            TIMUIKitCore.getInstance().loginUserInfo?.userID;
        debugPrint("红包点击事件， 消息详情: $currentUserID}");
        diyMsg['openUsers']?.add(currentUserID);

        // 是否已过期
        if (isEmpty) {
          diyMsg['status'] = 'expired';
          diyMsg['isOpen'] = false;
        }

        originMessage.customElem?.data = jsonEncode(diyMsg);
        debugPrint("消息修改成功: ${originMessage.customElem?.data.toString()}");
        V2TimValueCallback<V2TimMessageChangeInfo> modifyMessageRes =
            await TencentImSDKPlugin.v2TIMManager
                .getMessageManager()
                .modifyMessage(message: originMessage);
        if (modifyMessageRes.code == 0 && isEmpty == false) {
          if (modifyMessageRes.data?.code == 0) {
            if (message.groupID?.isNotEmpty == true) {
              // 修改成功, 发送领取红包消息
              _sendCustomMessage(context, message);
            } else {
              // 修改成功, 发送领取红包消息
              _sendCustomMessage(context, message);
            }
          }
        }
      }
    }
  }

  Future<String> _getFriendsInfo(List<String> userIDList) async {
    try {
      V2TimValueCallback<List<V2TimFriendInfoResult>> result =
          await TencentImSDKPlugin.v2TIMManager
              .getFriendshipManager()
              .getFriendsInfo(userIDList: userIDList);

      if (result.code == 0 && result.data != null) {
        debugPrint("获取好友信息成功: ${result.data?[0].toJson()}");
        String name = '';
        final data = result.data?[0].friendInfo;
        if (data != null && data.friendRemark?.isNotEmpty == true) {
          name = data.friendRemark ?? '';
        }
        return name;
      } else {
        debugPrint("获取好友信息失败: ${result.code} - ${result.desc}");
        return '';
      }
    } catch (e) {
      debugPrint("获取好友信息异常: $e");
      return '';
    }
  }

  Future<String> _getUserInfo(String userId) async {
    if (userId.isEmpty) {
      return '';
    }

    try {
      V2TimValueCallback<List<V2TimUserFullInfo>> result =
          await TencentImSDKPlugin.v2TIMManager
              .getUsersInfo(userIDList: [userId]);
      if (result.code == 0 && result.data != null && result.data!.isNotEmpty) {
        debugPrint("获取用户信息成功: ${result.data![0].nickName}");
        return result.data![0].nickName.toString();
      } else {
        debugPrint("获取用户信息失败: ${result.code} - ${result.desc}");
        return '';
      }
    } catch (e) {
      debugPrint("获取用户信息异常: $e");
      return '';
    }
  }

// 发送自定义领取红包消息
  _sendCustomMessage(BuildContext context, V2TimMessage message) async {
    // 创建自定义消息，下方的data/desc/extension可由您自行定义内容。
    //// '{"redEnvelopeID":"red_envelope","isOpen":false,"image":"https://pic1.imgdb.cn/item/6858f1bc58cb8da5c864cbb4/base.png","remark":"${data['remark']}","amount":"${data['amount']}","version":4}'

    String name = await _getFriendsInfo([message.sender.toString()]);
    if (name.isEmpty) {
      name = await _getUserInfo(message.sender.toString());
    }
    if (name.isEmpty) {
      name = message.sender.toString();
    }
    String myName = await _getUserInfo(myUserID);

    debugPrint('领取红包消息message.sender: $myName');
    V2TimValueCallback<V2TimMsgCreateInfoResult> createCustomMessageRes =
        await TencentImSDKPlugin.v2TIMManager
            .getMessageManager()
            .createCustomMessage(
                data:
                    '{ "receiveID": "red_envelope23", "remark": "领取了你的", "version": 4, "changeUser": "$name", "sender": "${message.sender}", "senderName": "$myName"}');
    if (createCustomMessageRes.code == 0 &&
        createCustomMessageRes.data != null &&
        createCustomMessageRes.data?.messageInfo != null) {
      String? id = createCustomMessageRes.data?.id;
      debugPrint('创建自定义消息111: ${createCustomMessageRes.data?.id}');
      // 发送自定义消息
      V2TimValueCallback<V2TimMessage>? sendMessageRes = await chatController
          .sendMessage(messageInfo: createCustomMessageRes.data!.messageInfo!);
      debugPrint('发送自定义消息222: ${sendMessageRes?.data?.customElem?.data}');
      if (sendMessageRes != null && sendMessageRes.code == 0) {
        // 发送成功
        sendMessageRes.data?.customElem?.data; //自定义data
        sendMessageRes.data?.customElem?.desc; //自定义desc
        sendMessageRes.data?.customElem?.extension; //自定义extension
      }
    }
  }

// 发送定向群消息
  _sendCustomMessage2Group(BuildContext context, V2TimMessage message) async {
    // 创建自定义消息，下方的data/desc/extension可由您自行定义内容。
    //// '{"redEnvelopeID":"red_envelope","isOpen":false,"image":"https://pic1.imgdb.cn/item/6858f1bc58cb8da5c864cbb4/base.png","remark":"${data['remark']}","amount":"${data['amount']}","version":4}'

    String name = await _getFriendsInfo([message.sender.toString()]);
    if (name.isEmpty) {
      name = await _getUserInfo(message.sender.toString());
    }
    if (name.isEmpty) {
      name = message.sender.toString();
    }

    debugPrint('领取红包消息的发送者id: ${message.sender}');
    V2TimValueCallback<V2TimMsgCreateInfoResult> createCustomMessageRes =
        await TencentImSDKPlugin.v2TIMManager
            .getMessageManager()
            .createCustomMessage(
                data:
                    '{ "receiveID": "red_envelope23", "remark": "领取了你的", "version": 4, "changeUser": "$name", "sender": "${message.sender}", "senderName": "${message.nickName}" }');
    if (createCustomMessageRes.code == 0 &&
        createCustomMessageRes.data != null &&
        createCustomMessageRes.data?.messageInfo != null) {
      String? id = createCustomMessageRes.data?.id;
      debugPrint('创建自定义消息111: ${createCustomMessageRes.data?.id}');
      // 创建定向群消息，指定接收者列表
      V2TimValueCallback<V2TimMsgCreateInfoResult> targetedMsgRes =
          await TencentImSDKPlugin.v2TIMManager
              .getMessageManager()
              .createTargetedGroupMessage(
                  id: id, // 使用刚创建的消息ID
                  receiverList: [message.sender.toString()] // 这里填写能看到消息的用户ID列表
                  );
      // 第三步：发送定向群消息
      if (targetedMsgRes.code == 0 && targetedMsgRes.data != null) {
        V2TimValueCallback<V2TimMessage>? sendMessageRes =
            await chatController.sendMessage(
                messageInfo: targetedMsgRes.data!.messageInfo!,
                groupID: message.groupID ?? '' // 指定群ID
                );

        if (sendMessageRes != null && sendMessageRes.code == 0) {
          // 发送成功
          debugPrint('定向群消息发送成功');
        }
      }
    }
  }

  @override
  State<CustomMessageElem> createState() => _CustomMessageElemState();
}

class _CustomMessageElemState extends State<CustomMessageElem> {
  bool isShowJumpState = false;
  bool isShining = false;
  bool isShowBorder = false;

  _showJumpColor() {
    isShining = true;
    int shineAmount = 6;
    setState(() {
      isShowJumpState = true;
      isShowBorder = true;
    });
    Timer.periodic(const Duration(milliseconds: 300), (timer) {
      if (mounted) {
        setState(() {
          isShowJumpState = shineAmount.isOdd ? true : false;
          isShowBorder = shineAmount.isOdd ? true : false;
        });
      }
      if (shineAmount == 0 || !mounted) {
        isShining = false;
        timer.cancel();
      }
      shineAmount--;
    });
    if (widget.clearJump != null) {
      widget.clearJump!();
    }
  }

  _redEnvelopeMessageStatus(messageInfo) {
    String _content = '';
    if(messageInfo?.status == 'expired'){
      _content = TIM_t("红包已过期");
      return _content;
    }
    if (messageInfo?.isOpen == true) {
      _content = TIM_t("已领取");
    } else {
      _content = "Phichat " + TIM_t("红包");
    }
    return _content;
  }

  Widget _callElemBuilder(BuildContext context, TUITheme theme) {
    final customElem = widget.message.customElem;

    final callingMessageDataProvider =
        CallingMessageDataProvider(widget.message);

    debugPrint('isForwardMessage222: ${widget.message.toJson()}');
    final packetReceiveMessage = getPacketReceiveMessage(customElem);
    final redEnvelopeMessage = getRedEnvelopeMessage(customElem);
    final linkMessage = getLinkMessage(customElem);
    final webLinkMessage = getWebLinkMessage(customElem);
    // 判断红包是否全部领取完毕
    bool isAllReceived = false;
    if (redEnvelopeMessage?.openUsers != null &&
        redEnvelopeMessage?.totalCount != null) {
      final openCount = redEnvelopeMessage!.openUsers!.length;
      final total = int.tryParse(redEnvelopeMessage.totalCount!) ?? 0;
      isAllReceived = openCount >= total;
    }
    if (customElem?.data == "group_create") {
      return renderMessageItem(
        Column(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(TIM_t(("群聊创建成功！"))),
          ],
        ),
        theme,
        false,
      );
    } else if (MessageUtils.getCustomGroupCreatedOrDismissedString(
            widget.message)
        .isNotEmpty) {
      return Container(
          margin: const EdgeInsets.symmetric(vertical: 20),
          alignment: Alignment.center,
          child: Text.rich(TextSpan(children: [
            TextSpan(
              text: MessageUtils.getCustomGroupCreatedOrDismissedString(
                  widget.message),
              style: TextStyle(color: theme.weakTextColor),
            ),
          ], style: const TextStyle(fontSize: 12))));
    } else if (packetReceiveMessage != null) {
      return _renderReceiveRedEnvelopeMessage(
          widget.message, packetReceiveMessage);
    } else if (redEnvelopeMessage != null) {
      // 先判断是否群红包
      var isGroup = redEnvelopeMessage.chatType == 'group';
      // 再查询openusers中是否包含自己的用户id
      var isReceived = IsReceivedRedPacket()
          .isReceivedRedPacket(redEnvelopeMessage.openUsers ?? []);
      return renderMessageItem(
        Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              redEnvelopeMessage.remark ?? "",
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
              style: const TextStyle(
                  fontSize: 14,
                  color: Colors.white,
                  fontWeight: FontWeight.w500),
            ),
            if (!isGroup && redEnvelopeMessage.isOpen == true)
              Text(TIM_t('已领取'),
                  style: const TextStyle(
                      fontSize: 10,
                      color: Colors.white,
                      fontWeight: FontWeight.w500))
            else if (isGroup && isReceived)
              Text(TIM_t('已领取'),
                  style: const TextStyle(
                      fontSize: 10,
                      color: Colors.white,
                      fontWeight: FontWeight.w500))
          ],
        ),
        theme,
        false,
        isRedEnvelopeMessage: true,
        image: redEnvelopeMessage.image,
        isDefault: redEnvelopeMessage.isDefault,
        messageInfo: redEnvelopeMessage,
        isGroup: isGroup,
        isReceived: isReceived,
        isAllReceived: isAllReceived,
      );
    } else if (linkMessage != null) {
      final String option1 = linkMessage.link ?? "";
      debugPrint('linkMessage: ${linkMessage.toString()}');
      return renderMessageItem(
        Column(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(linkMessage.text ?? ""),
            MarkdownBody(
              data: TIM_t_para(
                      "[查看详情111 >>]({{option1}})", "[查看详情11111 >>]($option1)")(
                  option1: option1),
              styleSheet: MarkdownStyleSheet.fromTheme(ThemeData(
                      textTheme: const TextTheme(
                          // ignore: deprecated_member_use
                          bodyMedium: TextStyle(fontSize: 16.0))))
                  .copyWith(
                a: TextStyle(color: LinkUtils.hexToColor("015fff")),
              ),
              onTapLink: (
                String link,
                String? href,
                String title,
              ) {
                LinkUtils.launchURL(context, linkMessage.link ?? "");
              },
            )
          ],
        ),
        theme,
        false,
      );
    } else if (webLinkMessage != null) {
      return renderMessageItem(
        Column(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text.rich(TextSpan(
                style: const TextStyle(
                  fontSize: 16,
                ),
                children: [
                  TextSpan(text: webLinkMessage.title),
                  TextSpan(
                    text: webLinkMessage.hyperlinks_text?["key"],
                    style: const TextStyle(
                      color: Color.fromRGBO(0, 110, 253, 1),
                    ),
                    recognizer: TapGestureRecognizer()
                      ..onTap = () {
                        CustomMessageElem.launchWebURL(
                          context,
                          webLinkMessage.hyperlinks_text?["value"],
                        );
                      },
                  )
                ])),
            if (webLinkMessage.description != null &&
                webLinkMessage.description!.isNotEmpty)
              Text(
                webLinkMessage.description!,
                style: const TextStyle(
                  fontSize: 16,
                ),
              )
          ],
        ),
        theme,
        false,
      );
    } else if (!callingMessageDataProvider.excludeFromHistory && callingMessageDataProvider.isCallingSignal) {
      if (callingMessageDataProvider.participantType == CallParticipantType.group) {
        // Group Call message
        return GroupCallMessageItem(callingMessageDataProvider: callingMessageDataProvider);
      } else {
        // One-to-one Call message
        bool isFromSelf = callingMessageDataProvider.direction == CallMessageDirection.outcoming;
        return renderMessageItem(
          CallMessageItem(callingMessageDataProvider: callingMessageDataProvider, padding: const EdgeInsets.all(0)),
          theme,
          false,
          isSelf: isFromSelf
        );
      }
    } else {
      return renderMessageItem(const Text("[自定义]"), theme, false);
    }
  }

  // 领取红包消息单独处理
  Widget _renderReceiveRedEnvelopeMessage(
      V2TimMessage message, dynamic messageInfo) {
    // 安全地检查 changeUser 属性是否存在

    var isGroup = widget.conversation?.type == 2;
    String fname = '';
    if (!isGroup) {
      fname = widget.conversation?.showName ?? '';
    } else {
      fname = messageInfo.changeUser ?? '';
      debugPrint('领取红包消息9999: ${messageInfo.sender} ${messageInfo.senderName}');
    }

    if (!isGroup) {
      return Padding(
          padding: const EdgeInsets.only(top: 16, bottom: 16),
          child: (widget.message.isSelf ?? false)
              ? Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Text(
                      TIM_t('你领取了'),
                      style: const TextStyle(
                          fontSize: 12,
                          color: Color(0xFF999999),
                          fontWeight: FontWeight.w500),
                    ),
                    const SizedBox(
                      width: 2,
                    ),
                    Text(
                      fname,
                      style: const TextStyle(
                          fontSize: 12,
                          color: Color(0xFF999999),
                          fontWeight: FontWeight.w500),
                    ),
                    const SizedBox(
                      width: 2,
                    ),
                    Text(
                      TIM_t('红包'),
                      style: const TextStyle(
                          fontSize: 12,
                          color: Color(0xFFFF5D5E),
                          fontWeight: FontWeight.w500),
                    ),
                  ],
                )
              : Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Text(
                      fname,
                      style: const TextStyle(
                          fontSize: 12,
                          color: Color(0xFF999999),
                          fontWeight: FontWeight.w500),
                    ),
                    const SizedBox(
                      width: 2,
                    ),
                    Text(
                      TIM_t('领取了你的'),
                      style: const TextStyle(
                          fontSize: 12,
                          color: Color(0xFF999999),
                          fontWeight: FontWeight.w500),
                    ),
                    const SizedBox(
                      width: 2,
                    ),
                    Text(
                      TIM_t('红包'),
                      style: const TextStyle(
                          fontSize: 12,
                          color: Color(0xFFFF5D5E),
                          fontWeight: FontWeight.w500),
                    ),
                  ],
                ));
    }
    return Padding(
        padding: const EdgeInsets.only(top: 16, bottom: 16),
        child: (widget.message.isSelf == true)
            ? Row(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Text(
                    TIM_t('你领取了'),
                    style: const TextStyle(
                        fontSize: 12,
                        color: Color(0xFF999999),
                        fontWeight: FontWeight.w500),
                  ),
                  const SizedBox(
                    width: 2,
                  ),
                  Text(
                    fname,
                    style: const TextStyle(
                        fontSize: 12,
                        color: Color(0xFF999999),
                        fontWeight: FontWeight.w500),
                  ),
                  const SizedBox(
                    width: 2,
                  ),
                  Text(
                    TIM_t('红包'),
                    style: const TextStyle(
                        fontSize: 12,
                        color: Color(0xFFFF5D5E),
                        fontWeight: FontWeight.w500),
                  ),
                ],
              )
            : Row(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Text(
                    messageInfo.senderName ?? '',
                    style: const TextStyle(
                        fontSize: 12,
                        color: Color(0xFF999999),
                        fontWeight: FontWeight.w500),
                  ),
                  const SizedBox(
                    width: 2,
                  ),
                  Text(
                    TIM_t('领取了'),
                    style: const TextStyle(
                        fontSize: 12,
                        color: Color(0xFF999999),
                        fontWeight: FontWeight.w500),
                  ),
                  Text(
                    fname,
                    style: const TextStyle(
                        fontSize: 12,
                        color: Color(0xFF999999),
                        fontWeight: FontWeight.w500),
                  ),
                  const SizedBox(
                    width: 2,
                  ),
                  Text(
                    TIM_t('红包'),
                    style: const TextStyle(
                        fontSize: 12,
                        color: Color(0xFFFF5D5E),
                        fontWeight: FontWeight.w500),
                  ),
                ],
              ));
  }

  Widget renderMessageItem(Widget child, TUITheme theme, bool isVoteMessage,
      {bool isRedEnvelopeMessage = false,
      String? image,
      bool? isDefault = false,
      RedEnvelopeMessage? messageInfo,
      bool? isGroup,
      bool? isReceived,
      bool? isAllReceived,
      bool? isSelf,
      }) {
    final isFromSelf = isSelf ?? widget.message.isSelf ?? true;
    final borderRadius = isFromSelf
        ? const BorderRadius.only(
            topLeft: Radius.circular(10),
            topRight: Radius.circular(10),
            bottomLeft: Radius.circular(10),
            bottomRight: Radius.circular(10))
        : const BorderRadius.only(
            topLeft: Radius.circular(10),
            topRight: Radius.circular(10),
            bottomLeft: Radius.circular(10),
            bottomRight: Radius.circular(10));

    final defaultStyle = isFromSelf
        ? theme.lightPrimaryMaterialColor.shade50
        : Colors.white;
    final backgroundColor =
        isShowJumpState ? const Color.fromRGBO(245, 166, 35, 1) : defaultStyle;
    final _image = image ?? "";
    final _isDefault = isDefault ?? false;
    bool isRedPacketReceived = false;
    if (isGroup == false && messageInfo?.isOpen == true) {
      isRedPacketReceived = true;
    } else if (isGroup == true && isReceived == true) {
      isRedPacketReceived = true;
    }
    else if (isGroup == true && isAllReceived == true) {
      isRedPacketReceived = true;
    }
    if (messageInfo?.status == 'expired') {
      isRedPacketReceived = true;
    }
    if (isRedEnvelopeMessage) {
      // 默认红包，使用新样式
      if (_isDefault == true) {
        // 判断是否已领取

        return GestureDetector(
          onTap: () {
            widget.handleRedEnvelopeClick(context, widget.message, messageInfo);
          },
          child: Container(
              width: 186,
              decoration: BoxDecoration(
                borderRadius: widget.messageBorderRadius ?? borderRadius,
                gradient: LinearGradient(
                  begin: Alignment.centerLeft,
                  end: Alignment.centerRight,
                  colors: isRedPacketReceived
                      ? [
                          const Color(0xFFFFE3C0), // 已领取：灰色
                          const Color(0xFFFC981D), // 橙黄色
                        ]
                      : [
                          const Color(0xFFFC981D), // 橙红色
                          const Color(0xFFFC981D), // 橙黄色
                        ],
                ),
              ),
              child: Column(
                children: [
                  Container(
                      padding: const EdgeInsets.fromLTRB(12, 12, 12, 8),
                      child: Row(
                        children: [
                          // 左侧红色圆形图标

                          Image.asset(
                            isRedPacketReceived
                                ? 'assets/red_envelope/envelope_icon_active.png'
                                : 'assets/red_envelope/envelope_icon.png',
                          ),
                          const SizedBox(width: 12),
                          // 右侧文字内容
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Text(
                                  messageInfo?.remark ?? "",
                                  style: const TextStyle(
                                    fontSize: 14,
                                    color: Colors.white,
                                    fontWeight: FontWeight.w500,
                                  ),
                                  maxLines: 1,
                                  overflow: TextOverflow.ellipsis,
                                ),
                                const SizedBox(height: 2),
                              ],
                            ),
                          ),
                        ],
                      )),
                  const Divider(
                    height: 0.5,
                    thickness: 1,
                    color: Color(0x70E5E5E5),
                  ),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      Text(
                        _redEnvelopeMessageStatus(messageInfo),
                        textAlign: TextAlign.right,
                        style: const TextStyle(
                          fontSize: 12,
                          color: Colors.white70,
                          fontWeight: FontWeight.w400,
                        ),
                      ),
                      const SizedBox(width: 12),
                    ],
                  ),
                  const SizedBox(height: 8),
                ],
              )),
        );
      }

      // 保持原有的非默认红包样式
      return GestureDetector(
        onTap: () {
          widget.handleRedEnvelopeClick(context, widget.message, messageInfo);
        },
        child: Container(
            width: 186,
            decoration: BoxDecoration(
              borderRadius: widget.messageBorderRadius ?? borderRadius,
              image: DecorationImage(
                image: CachedNetworkImageProvider(
                  _image,
                  cacheKey: _image,
                ),
                fit: BoxFit.cover,

                alignment: const Alignment(0, -0.5), // x轴居中，y轴偏上
              ),
            ),
            child: Stack(
              children: [
                if (isRedPacketReceived)
                  Positioned(
                    top: 0,
                    left: 0,
                    right: 0,
                    bottom: 0,
                    child: Container(
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          begin: Alignment.centerLeft,
                          end: Alignment.centerRight,
                          colors: [
                            const Color(0xFFFFE3C0), // 橙色//

                            const Color(0xFFFFE3C0).withOpacity(.3), // 已领取：灰色
                          ],
                        ),
                      ),
                    ),
                  ),
                Column(
                  children: [
                    Container(
                      padding: const EdgeInsets.fromLTRB(12, 12, 12, 8),
                      child: Row(
                        children: [
                          // 左侧红色圆形图标
                          Image.asset(
                            // 根据不同状态显示不同图标
                            ((isGroup == false &&
                                        messageInfo?.isOpen == true) ||
                                    (isGroup == true && isReceived == true) ||
                                    isAllReceived == true)
                                ? 'assets/red_envelope/envelope_icon_active.png'
                                : 'assets/red_envelope/envelope_icon.png',
                          ),
                          const SizedBox(width: 12),
                          // 右侧文字内容
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Text(
                                  messageInfo?.remark ?? "",
                                  style: const TextStyle(
                                    fontSize: 14,
                                    color: Colors.white,
                                    fontWeight: FontWeight.w500,
                                  ),
                                  maxLines: 1,
                                  overflow: TextOverflow.ellipsis,
                                ),
                                const SizedBox(height: 2),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                    const Divider(
                      height: 0.5,
                      thickness: 1,
                      color: Color(0x70E5E5E5),
                    ),
                    const Row(
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        Text(
                          "Send blessings",
                          textAlign: TextAlign.right,
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.white70,
                            fontWeight: FontWeight.w400,
                          ),
                        ),
                        SizedBox(width: 12),
                      ],
                    ),
                    const SizedBox(height: 8),
                  ],
                ),
              ],
            )),
      );
    }
    return Container(
        padding: isVoteMessage
            ? null
            : (widget.textPadding ?? const EdgeInsets.all(10)),
        decoration: isVoteMessage
            ? BoxDecoration(
                border: Border.all(
                    width: 1, color: theme.weakDividerColor ?? Colors.grey))
            : BoxDecoration(
                color: widget.messageBackgroundColor ?? backgroundColor,
                borderRadius: widget.messageBorderRadius ?? borderRadius,
              ),
        constraints: BoxConstraints(
            maxWidth:
                isVoteMessage ? 298 : 240), // vote message width need more
        child: child);
  }

  @override
  Widget build(BuildContext context) {
    final theme = Provider.of<DefaultThemeData>(context).theme;

    if (widget.isShowJump) {
      if (!isShining) {
        Future.delayed(Duration.zero, () {
          _showJumpColor();
        });
      } else {
        if (widget.clearJump != null) {
          widget.clearJump!();
        }
      }
    }

    return _callElemBuilder(context, theme);
  }
}
